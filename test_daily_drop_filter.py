"""
Test Script for Daily Drop Filter
Verify that the daily drop filter blocks trades when daily drop >= 3%
"""

import pandas as pd
import numpy as np
from config import FullAnalysisConfig
from backtesting_engine import BacktestingEngine, calculate_daily_drop_worker
from indicators import TechnicalIndicators


def create_test_data_with_drop():
    """Create test data with a significant daily drop"""
    # Create data with a 5% drop from daily high
    data = {
        'close': [100, 102, 104, 106, 105, 103, 101, 99, 97, 95],  # 5% drop from high of 106
        'open': [99, 101, 103, 105, 104, 102, 100, 98, 96, 94],
        'high': [101, 103, 105, 106, 106, 104, 102, 100, 98, 96],
        'low': [98, 100, 102, 104, 103, 101, 99, 97, 95, 93],
        'volume': [1000] * 10
    }
    
    df = pd.DataFrame(data)
    return df


def test_daily_drop_calculation():
    """Test the daily drop calculation function"""
    print("🧪 Testing Daily Drop Calculation")
    print("=" * 50)
    
    df = create_test_data_with_drop()
    
    # Test at different points
    test_cases = [
        (0, 0.0, "First candle - no drop"),
        (3, 0.0, "At daily high - no drop"),
        (6, 4.72, "After drop - should show ~4.7% drop"),
        (9, 10.38, "At lowest point - should show ~10.4% drop"),
    ]
    
    all_passed = True
    
    for idx, expected_drop, description in test_cases:
        actual_drop = calculate_daily_drop_worker(df, idx)
        
        # Allow 0.5% tolerance for floating point precision
        if abs(actual_drop - expected_drop) <= 0.5:
            print(f"✅ {description}: {actual_drop:.2f}% (expected ~{expected_drop:.2f}%)")
        else:
            print(f"❌ {description}: {actual_drop:.2f}% (expected ~{expected_drop:.2f}%)")
            all_passed = False
    
    return all_passed


def test_filter_configuration():
    """Test that the daily drop filter configuration is properly set"""
    print("\n🔧 Testing Filter Configuration")
    print("=" * 50)
    
    config = FullAnalysisConfig()
    
    # Check configuration values
    tests = [
        ("ENABLE_DAILY_DROP_FILTER", True),
        ("DAILY_DROP_THRESHOLD", 3.0),
    ]
    
    all_passed = True
    
    for attr_name, expected_value in tests:
        actual_value = getattr(config, attr_name, None)
        if actual_value == expected_value:
            print(f"✅ {attr_name}: {actual_value}")
        else:
            print(f"❌ {attr_name}: Expected {expected_value}, got {actual_value}")
            all_passed = False
    
    return all_passed


def test_filter_blocking():
    """Test that the filter actually blocks trades during drops"""
    print("\n🚫 Testing Trade Blocking During Drops")
    print("=" * 50)
    
    # Create test data with significant drop
    df = create_test_data_with_drop()
    
    # Add required technical indicators
    indicators = TechnicalIndicators(df)
    df = indicators.calculate_all_indicators()
    
    # Test configuration with daily drop filter enabled
    config = FullAnalysisConfig()
    config.ENABLE_DAILY_DROP_FILTER = True
    config.DAILY_DROP_THRESHOLD = 3.0
    
    # Initialize engine
    engine = BacktestingEngine(df, config)
    
    # Test global market filters at different points
    test_cases = [
        (3, True, "At daily high - should allow trades"),
        (6, False, "After 4.7% drop - should block trades"),
        (9, False, "After 10.4% drop - should block trades"),
    ]
    
    all_passed = True
    
    for idx, should_allow, description in test_cases:
        try:
            # Test the global market filter
            result = engine._check_global_market_filters(idx)
            
            if result == should_allow:
                status = "✅" if should_allow else "🚫"
                print(f"{status} {description}: {'Allowed' if result else 'Blocked'}")
            else:
                print(f"❌ {description}: Expected {'Allow' if should_allow else 'Block'}, got {'Allow' if result else 'Block'}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
            all_passed = False
    
    return all_passed


def test_worker_filter_blocking():
    """Test that the worker filter also blocks trades during drops"""
    print("\n⚡ Testing Worker Filter Blocking")
    print("=" * 50)
    
    # Create test data with significant drop
    df = create_test_data_with_drop()
    
    # Prepare worker config
    config = {
        'ENABLE_GLOBAL_MARKET_FILTERS': True,
        'ENABLE_DAILY_DROP_FILTER': True,
        'DAILY_DROP_THRESHOLD': 3.0,
        'GLOBAL_EMA200_FILTER': False,  # Disable other filters for this test
        'GLOBAL_RSI_OVERBOUGHT_FILTER': False,
    }
    
    # Test worker function at different points
    test_cases = [
        (3, True, "At daily high - should allow trades"),
        (6, False, "After 4.7% drop - should block trades"),
        (9, False, "After 10.4% drop - should block trades"),
    ]
    
    all_passed = True
    
    for idx, should_allow, description in test_cases:
        try:
            from backtesting_engine import check_global_market_filters_worker
            result = check_global_market_filters_worker(df, idx, config)
            
            if result == should_allow:
                status = "✅" if should_allow else "🚫"
                print(f"{status} {description}: {'Allowed' if result else 'Blocked'}")
            else:
                print(f"❌ {description}: Expected {'Allow' if should_allow else 'Block'}, got {'Allow' if result else 'Block'}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
            all_passed = False
    
    return all_passed


def test_real_data_scenario():
    """Test with real-like market data scenario"""
    print("\n📊 Testing Real Market Scenario")
    print("=" * 50)
    
    try:
        # Try to load real data
        df = pd.read_csv('selected_data.csv')
        print(f"✅ Loaded {len(df):,} candles from selected_data.csv")
        
        # Calculate indicators
        indicators = TechnicalIndicators(df)
        df = indicators.calculate_all_indicators()
        
        # Test configuration
        config = FullAnalysisConfig()
        engine = BacktestingEngine(df, config)
        
        # Find a period with significant drop
        drops_found = 0
        blocks_found = 0
        
        for i in range(1440, min(5000, len(df))):  # Test a sample range
            daily_drop = engine._calculate_daily_drop(i)
            
            if daily_drop >= 3.0:
                drops_found += 1
                
                # Check if filter blocks trades
                filter_result = engine._check_global_market_filters(i)
                if not filter_result:
                    blocks_found += 1
        
        print(f"📈 Analysis results:")
        print(f"   - Periods with ≥3% daily drop: {drops_found}")
        print(f"   - Periods where trades blocked: {blocks_found}")
        
        if drops_found > 0:
            block_rate = (blocks_found / drops_found) * 100
            print(f"   - Block rate during drops: {block_rate:.1f}%")
            
            if block_rate >= 90:  # Should block most of the time during drops
                print("✅ Filter working correctly on real data")
                return True
            else:
                print("⚠️ Filter not blocking enough during drops")
                return False
        else:
            print("⚠️ No significant drops found in test period")
            return True  # Not a failure, just no test data
            
    except Exception as e:
        print(f"⚠️ Could not test with real data: {e}")
        return True  # Not a failure, just no real data available


def main():
    """Run all tests"""
    print("🚀 DAILY DROP FILTER TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_daily_drop_calculation,
        test_filter_configuration,
        test_filter_blocking,
        test_worker_filter_blocking,
        test_real_data_scenario,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Daily drop filter implemented correctly")
        print("✅ Trades will be blocked when daily drop ≥ 3%")
        print("✅ Filter works in both unified and multiprocessing modes")
        print("\nThe system will now protect against entering trades during significant daily drops!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the error messages above")


if __name__ == "__main__":
    main()

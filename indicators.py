"""
Technical Indicators Module
Comprehensive technical indicators for all trading rules
"""

import pandas as pd
import numpy as np
import talib
from config import Config


class TechnicalIndicators:
    """Calculate all technical indicators needed for trading rules"""

    def __init__(self, df):
        """Initialize with OHLCV dataframe"""
        self.df = df.copy()
        self.config = Config()

        # Extract price and volume arrays for talib
        self.high = df['high'].values
        self.low = df['low'].values
        self.close = df['close'].values
        self.open = df['open'].values
        self.volume = df['volume'].values

        # Cache reference for ultra-fast access
        self.cache_engine = None
        self.cache_start_idx = 0

        print("Technical Indicators initialized")

    def set_cache_engine(self, cache_engine, cache_start_idx):
        """Set reference to cached indicators for ultra-fast access"""
        self.cache_engine = cache_engine
        self.cache_start_idx = cache_start_idx
    
    def calculate_all_indicators(self):
        """Calculate all technical indicators"""
        print("Calculating technical indicators...")
        
        # Basic price indicators
        self._calculate_moving_averages()
        self._calculate_momentum_indicators()
        self._calculate_volatility_indicators()
        self._calculate_volume_indicators()
        self._calculate_trend_indicators()
        self._calculate_support_resistance()
        self._calculate_pattern_indicators()
        self._calculate_advanced_indicators()
        
        print(f"Technical indicators calculated for {len(self.df)} candles")
        return self.df

    def _calculate_daily_indicators(self):
        """Calculate daily indicators by resampling 1-minute data to daily"""
        try:
            # Create a copy of the dataframe with datetime index for resampling
            df_copy = self.df.copy()

            # If there's no datetime column, create one based on index (assuming 1-minute intervals)
            if 'datetime' not in df_copy.columns:
                # Create datetime index assuming data starts from a base date
                import datetime
                base_date = datetime.datetime(2023, 1, 1)  # Arbitrary start date
                df_copy['datetime'] = pd.date_range(start=base_date, periods=len(df_copy), freq='1min')

            # Set datetime as index for resampling
            df_copy.set_index('datetime', inplace=True)

            # Resample to daily data (OHLC)
            daily_data = df_copy.resample('1D').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()

            # Calculate daily EMA200 and RSI on daily data
            daily_ema200 = talib.EMA(daily_data['close'].values, timeperiod=200)
            daily_rsi = talib.RSI(daily_data['close'].values, timeperiod=14)

            # Create a mapping from daily values back to minute data
            # Forward fill daily values to all minutes in each day
            daily_data['DAILY_EMA_200'] = daily_ema200
            daily_data['DAILY_RSI'] = daily_rsi

            # Reset index to get datetime as column
            daily_data.reset_index(inplace=True)

            # Create minute-level mapping by forward-filling daily values
            df_copy.reset_index(inplace=True)
            df_copy['date'] = df_copy['datetime'].dt.date
            daily_data['date'] = daily_data['datetime'].dt.date

            # Merge daily indicators back to minute data
            merged = df_copy.merge(daily_data[['date', 'DAILY_EMA_200', 'DAILY_RSI']],
                                 on='date', how='left')

            # Forward fill any missing values
            merged['DAILY_EMA_200'] = merged['DAILY_EMA_200'].fillna(method='ffill')
            merged['DAILY_RSI'] = merged['DAILY_RSI'].fillna(method='ffill')

            # Add the daily indicators to the main dataframe
            self.df['DAILY_EMA_200'] = merged['DAILY_EMA_200'].values
            self.df['DAILY_RSI'] = merged['DAILY_RSI'].values

            print(f"✅ Daily indicators calculated: EMA200 and RSI")

        except Exception as e:
            print(f"⚠️ Warning: Could not calculate daily indicators: {e}")
            # Fallback: use longer period indicators on minute data
            self.df['DAILY_EMA_200'] = talib.EMA(self.close, timeperiod=min(2000, len(self.close)//2))
            self.df['DAILY_RSI'] = talib.RSI(self.close, timeperiod=min(200, len(self.close)//10))

    def _calculate_daily_rsi_proper(self):
        """Calculate daily RSI the same way as Binance (RSI14 on daily candles)"""
        try:
            # Create a copy of the dataframe with datetime index for resampling
            df_copy = self.df.copy()

            # If there's no datetime column, create one based on index (assuming 1-minute intervals)
            if 'datetime' not in df_copy.columns:
                import datetime
                base_date = datetime.datetime(2023, 1, 1)  # Arbitrary start date
                df_copy['datetime'] = pd.date_range(start=base_date, periods=len(df_copy), freq='1min')

            # Set datetime as index for resampling
            df_copy.set_index('datetime', inplace=True)

            # Resample to daily data (OHLC) - this creates daily candles
            daily_data = df_copy.resample('1D').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()

            # Calculate RSI(14) on daily candles - this matches Binance
            daily_rsi_values = talib.RSI(daily_data['close'].values, timeperiod=14)
            daily_data['DAILY_RSI'] = daily_rsi_values

            # Create a mapping from daily values back to minute data
            daily_data.reset_index(inplace=True)
            df_copy.reset_index(inplace=True)
            df_copy['date'] = df_copy['datetime'].dt.date
            daily_data['date'] = daily_data['datetime'].dt.date

            # Merge daily RSI back to minute data (forward fill within each day)
            merged = df_copy.merge(daily_data[['date', 'DAILY_RSI']], on='date', how='left')

            # Forward fill any missing values
            merged['DAILY_RSI'] = merged['DAILY_RSI'].fillna(method='ffill')

            # Add the daily RSI to the main dataframe
            self.df['DAILY_RSI'] = merged['DAILY_RSI'].values

            # Check the range of values
            valid_rsi = self.df['DAILY_RSI'].dropna()
            if len(valid_rsi) > 0:
                min_rsi = valid_rsi.min()
                max_rsi = valid_rsi.max()
                print(f"✅ Daily RSI calculated properly: Range {min_rsi:.1f} to {max_rsi:.1f} (like Binance)")

        except Exception as e:
            print(f"⚠️ Error calculating proper daily RSI: {e}")
            # Fallback to simple calculation
            self.df['DAILY_RSI'] = talib.RSI(self.close, timeperiod=min(200, len(self.close)//10))

    def _calculate_daily_ema200_proper(self):
        """Calculate daily EMA200 the same way as Binance (EMA200 on daily candles)"""
        try:
            # Create a copy of the dataframe with datetime index for resampling
            df_copy = self.df.copy()

            # If there's no datetime column, create one based on index (assuming 1-minute intervals)
            if 'datetime' not in df_copy.columns:
                import datetime
                base_date = datetime.datetime(2023, 1, 1)  # Arbitrary start date
                df_copy['datetime'] = pd.date_range(start=base_date, periods=len(df_copy), freq='1min')

            # Set datetime as index for resampling
            df_copy.set_index('datetime', inplace=True)

            # Resample to daily data (OHLC) - this creates daily candles
            daily_data = df_copy.resample('1D').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()

            # Calculate EMA(200) on daily candles - this matches Binance
            daily_ema200_values = talib.EMA(daily_data['close'].values, timeperiod=200)
            daily_data['DAILY_EMA_200'] = daily_ema200_values

            # Create a mapping from daily values back to minute data
            daily_data.reset_index(inplace=True)
            df_copy.reset_index(inplace=True)
            df_copy['date'] = df_copy['datetime'].dt.date
            daily_data['date'] = daily_data['datetime'].dt.date

            # Merge daily EMA200 back to minute data (forward fill within each day)
            merged = df_copy.merge(daily_data[['date', 'DAILY_EMA_200']], on='date', how='left')

            # Forward fill any missing values
            merged['DAILY_EMA_200'] = merged['DAILY_EMA_200'].fillna(method='ffill')

            # Add the daily EMA200 to the main dataframe
            self.df['DAILY_EMA_200'] = merged['DAILY_EMA_200'].values

            # Check the calculation
            valid_ema = self.df['DAILY_EMA_200'].dropna()
            if len(valid_ema) > 0:
                print(f"✅ Daily EMA200 calculated properly: {len(valid_ema):,} valid values (like Binance)")

        except Exception as e:
            print(f"⚠️ Error calculating proper daily EMA200: {e}")
            # Fallback to simple calculation
            self.df['DAILY_EMA_200'] = talib.EMA(self.close, timeperiod=min(2000, len(self.close)//2))

    def _calculate_moving_averages(self):
        """Calculate moving averages"""
        # Simple Moving Averages
        periods = [5, 7, 9, 10, 20, 25, 50, 100, 200]
        for period in periods:
            self.df[f'SMA_{period}'] = talib.SMA(self.close, timeperiod=period)
        
        # Exponential Moving Averages
        ema_periods = [5, 7, 9, 12, 21, 26, 50, 200]
        for period in ema_periods:
            self.df[f'EMA_{period}'] = talib.EMA(self.close, timeperiod=period)

        # Daily EMA200 - calculate on resampled daily data (like Binance)
        # This will give us the same EMA200 values you see on trading platforms
        self._calculate_daily_ema200_proper()
        
        # Weighted Moving Average
        self.df['WMA_20'] = talib.WMA(self.close, timeperiod=20)
        
        # Hull Moving Average (approximation)
        self.df['HMA_20'] = self._calculate_hull_ma(20)
        
        # Legacy aliases for backward compatibility
        self.df['MA7'] = self.df['SMA_7']
        self.df['MA25'] = self.df['SMA_25']
        self.df['MA50'] = self.df['SMA_50']
        self.df['MA200'] = self.df['SMA_200']
    
    def _calculate_momentum_indicators(self):
        """Calculate momentum indicators"""
        # RSI
        self.df['RSI'] = talib.RSI(self.close, timeperiod=self.config.INDICATOR_PERIODS['RSI_PERIOD'])
        self.df['RSI_9'] = talib.RSI(self.close, timeperiod=9)

        # Daily RSI - calculate on resampled daily data (like Binance)
        # This will give us the same RSI values you see on trading platforms
        self._calculate_daily_rsi_proper()


        
        # MACD
        macd, macd_signal, macd_hist = talib.MACD(
            self.close,
            fastperiod=self.config.INDICATOR_PERIODS['MACD_FAST'],
            slowperiod=self.config.INDICATOR_PERIODS['MACD_SLOW'],
            signalperiod=self.config.INDICATOR_PERIODS['MACD_SIGNAL']
        )
        self.df['MACD'] = macd
        self.df['MACD_signal'] = macd_signal
        self.df['MACD_hist'] = macd_hist
        
        # Stochastic
        slowk, slowd = talib.STOCH(
            self.high, self.low, self.close,
            fastk_period=self.config.INDICATOR_PERIODS['STOCH_PERIOD'],
            slowk_period=3, slowd_period=3
        )
        self.df['STOCH_K'] = slowk
        self.df['STOCH_D'] = slowd
        
        # Williams %R
        self.df['WILLIAMS_R'] = talib.WILLR(self.high, self.low, self.close, timeperiod=14)
        
        # Rate of Change
        self.df['ROC'] = talib.ROC(self.close, timeperiod=10)
        self.df['ROC_5'] = talib.ROC(self.close, timeperiod=5)
        
        # Commodity Channel Index
        self.df['CCI'] = talib.CCI(self.high, self.low, self.close, timeperiod=14)
        
        # Momentum
        self.df['MOM'] = talib.MOM(self.close, timeperiod=10)

        # Additional indicators for new rules
        self._calculate_ppo()
        self._calculate_kst()
        self._calculate_coppock()
        self._calculate_ultimate_oscillator()
        self._calculate_supertrend()

        # Advanced indicators for professional rules
        self._calculate_tsi()
        self._calculate_connors_rsi()
        self._calculate_vortex_indicator()
        self._calculate_donchian_channels()

        # New indicators for enhanced rules
        self._calculate_kama()
        self._calculate_tema()
        self._calculate_parabolic_sar_enhanced()
    
    def _calculate_volatility_indicators(self):
        """Calculate volatility indicators"""
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(
            self.close,
            timeperiod=self.config.INDICATOR_PERIODS['BB_PERIOD'],
            nbdevup=self.config.INDICATOR_PERIODS['BB_STD'],
            nbdevdn=self.config.INDICATOR_PERIODS['BB_STD']
        )
        self.df['BB_upper'] = bb_upper
        self.df['BB_middle'] = bb_middle
        self.df['BB_lower'] = bb_lower
        self.df['BB_width'] = (bb_upper - bb_lower) / bb_middle
        self.df['BB_position'] = (self.close - bb_lower) / (bb_upper - bb_lower)
        
        # Average True Range
        self.df['ATR'] = talib.ATR(self.high, self.low, self.close, 
                                  timeperiod=self.config.INDICATOR_PERIODS['ATR_PERIOD'])
        self.df['ATR_20'] = talib.ATR(self.high, self.low, self.close, timeperiod=20)
        
        # True Range
        self.df['TR'] = talib.TRANGE(self.high, self.low, self.close)
        
        # Standard Deviation
        self.df['STDDEV'] = talib.STDDEV(self.close, timeperiod=20)
    
    def _calculate_volume_indicators(self):
        """Calculate volume indicators"""
        # Volume Moving Averages
        self.df['volume_sma_20'] = talib.SMA(self.volume, timeperiod=20)
        self.df['volume_sma_50'] = talib.SMA(self.volume, timeperiod=50)
        self.df['volume_ratio'] = self.volume / self.df['volume_sma_20']
        
        # On-Balance Volume
        self.df['OBV'] = talib.OBV(self.close, self.volume)
        
        # Accumulation/Distribution Line
        self.df['AD'] = talib.AD(self.high, self.low, self.close, self.volume)
        
        # Chaikin Money Flow
        self.df['CMF'] = self._calculate_cmf()
        
        # Volume Weighted Average Price
        self.df['VWAP'] = self._calculate_vwap()
        
        # Money Flow Index
        self.df['MFI'] = talib.MFI(self.high, self.low, self.close, self.volume, timeperiod=14)
        
        # Volume Price Trend
        self.df['VPT'] = self._calculate_vpt()
        
        # Legacy alias
        self.df['volume_sma'] = self.df['volume_sma_20']
    
    def _calculate_trend_indicators(self):
        """Calculate trend indicators"""
        # ADX and Directional Movement
        self.df['ADX'] = talib.ADX(self.high, self.low, self.close, timeperiod=14)
        self.df['PLUS_DI'] = talib.PLUS_DI(self.high, self.low, self.close, timeperiod=14)
        self.df['MINUS_DI'] = talib.MINUS_DI(self.high, self.low, self.close, timeperiod=14)
        
        # Parabolic SAR
        self.df['SAR'] = talib.SAR(self.high, self.low, acceleration=0.02, maximum=0.2)
        
        # Aroon
        aroon_down, aroon_up = talib.AROON(self.high, self.low, timeperiod=14)
        self.df['AROON_UP'] = aroon_up
        self.df['AROON_DOWN'] = aroon_down
        self.df['AROON_OSC'] = aroon_up - aroon_down
        
        # TRIX
        self.df['TRIX'] = talib.TRIX(self.close, timeperiod=14)
    
    def _calculate_support_resistance(self):
        """Calculate support and resistance levels"""
        # Dynamic support/resistance using pivot points
        self.df['PIVOT'] = (self.high + self.low + self.close) / 3
        self.df['R1'] = 2 * self.df['PIVOT'] - self.low
        self.df['S1'] = 2 * self.df['PIVOT'] - self.high
        self.df['R2'] = self.df['PIVOT'] + (self.high - self.low)
        self.df['S2'] = self.df['PIVOT'] - (self.high - self.low)
        
        # Rolling support/resistance
        window = 20
        self.df['resistance'] = self.df['high'].rolling(window=window, center=False).max()
        self.df['support'] = self.df['low'].rolling(window=window, center=False).min()
        
        # Forward fill the levels
        self.df['resistance'] = self.df['resistance'].fillna(method='ffill')
        self.df['support'] = self.df['support'].fillna(method='ffill')
        
        # Backward fill for initial values
        self.df['resistance'] = self.df['resistance'].fillna(method='bfill')
        self.df['support'] = self.df['support'].fillna(method='bfill')
    
    def _calculate_pattern_indicators(self):
        """Calculate pattern recognition indicators"""
        # Price change indicators
        self.df['price_change'] = self.close - np.roll(self.close, 1)
        self.df['price_change_pct'] = (self.close / np.roll(self.close, 1) - 1) * 100
        
        # High/Low patterns
        self.df['higher_high'] = (self.high > np.roll(self.high, 1)).astype(int)
        self.df['lower_low'] = (self.low < np.roll(self.low, 1)).astype(int)
        
        # Gap detection
        self.df['gap_up'] = (self.low > np.roll(self.high, 1)).astype(int)
        self.df['gap_down'] = (self.high < np.roll(self.low, 1)).astype(int)
    
    def _calculate_advanced_indicators(self):
        """Calculate advanced indicators"""
        # Fibonacci levels (dynamic)
        self._calculate_fibonacci_levels()
        
        # Market structure
        self._calculate_market_structure()
        
        # Order flow approximations
        self._calculate_order_flow()
    
    def _calculate_hull_ma(self, period):
        """Calculate Hull Moving Average"""
        half_period = int(period / 2)
        sqrt_period = int(np.sqrt(period))
        
        wma_half = talib.WMA(self.close, timeperiod=half_period)
        wma_full = talib.WMA(self.close, timeperiod=period)
        
        raw_hma = 2 * wma_half - wma_full
        return talib.WMA(raw_hma, timeperiod=sqrt_period)
    
    def _calculate_cmf(self, period=20):
        """Calculate Chaikin Money Flow"""
        mfv = ((self.close - self.low) - (self.high - self.close)) / (self.high - self.low) * self.volume
        mfv = np.where(self.high == self.low, 0, mfv)  # Handle division by zero
        cmf = pd.Series(mfv).rolling(window=period).sum() / pd.Series(self.volume).rolling(window=period).sum()
        return cmf.values
    
    def _calculate_vwap(self):
        """Calculate Volume Weighted Average Price"""
        typical_price = (self.high + self.low + self.close) / 3
        vwap = np.cumsum(typical_price * self.volume) / np.cumsum(self.volume)
        return vwap
    
    def _calculate_vpt(self):
        """Calculate Volume Price Trend"""
        price_change_pct = np.diff(self.close) / self.close[:-1]
        vpt = np.zeros(len(self.close))
        vpt[1:] = np.cumsum(self.volume[1:] * price_change_pct)
        return vpt
    
    def _calculate_fibonacci_levels(self, lookback_period=100):
        """Calculate dynamic Fibonacci retracement levels"""
        high_series = pd.Series(self.high).rolling(window=lookback_period).max()
        low_series = pd.Series(self.low).rolling(window=lookback_period).min()
        
        diff = high_series - low_series
        
        self.df['fib_23.6'] = high_series - 0.236 * diff
        self.df['fib_38.2'] = high_series - 0.382 * diff
        self.df['fib_50.0'] = high_series - 0.500 * diff
        self.df['fib_61.8'] = high_series - 0.618 * diff
        self.df['fib_78.6'] = high_series - 0.786 * diff
    
    def _calculate_market_structure(self):
        """Calculate market structure indicators"""
        # Higher highs and lower lows
        window = 10
        self.df['HH'] = self.df['high'].rolling(window=window).max()
        self.df['LL'] = self.df['low'].rolling(window=window).min()
        
        # Market structure breaks
        self.df['structure_break_up'] = (self.df['high'] > self.df['HH'].shift(1)).astype(int)
        self.df['structure_break_down'] = (self.df['low'] < self.df['LL'].shift(1)).astype(int)
    
    def _calculate_order_flow(self):
        """Calculate order flow approximation indicators"""
        # Delta (approximation using tick data simulation)
        self.df['delta'] = np.where(self.close > self.open, self.volume, -self.volume)
        self.df['cumulative_delta'] = self.df['delta'].cumsum()
        
        # Volume imbalance
        self.df['volume_imbalance'] = self.df['volume'] - self.df['volume_sma_20']
        
        # Buying/Selling pressure
        self.df['buying_pressure'] = np.where(self.close > (self.high + self.low) / 2, self.volume, 0)
        self.df['selling_pressure'] = np.where(self.close < (self.high + self.low) / 2, self.volume, 0)

    def _calculate_ppo(self, short=9, long=26):
        """Calculate Percentage Price Oscillator"""
        ema_short = talib.EMA(self.close, timeperiod=short)
        ema_long = talib.EMA(self.close, timeperiod=long)
        ppo = ((ema_short - ema_long) / ema_long) * 100
        self.df['PPO'] = ppo

    def _calculate_kst(self):
        """Calculate KST Oscillator"""
        # Rate of Change calculations
        roc1 = talib.ROC(self.close, timeperiod=10)
        roc2 = talib.ROC(self.close, timeperiod=15)
        roc3 = talib.ROC(self.close, timeperiod=20)
        roc4 = talib.ROC(self.close, timeperiod=30)

        # Moving averages of ROCs
        mov1 = talib.SMA(roc1, timeperiod=10)
        mov2 = talib.SMA(roc2, timeperiod=10)
        mov3 = talib.SMA(roc3, timeperiod=10)
        mov4 = talib.SMA(roc4, timeperiod=15)

        # KST calculation
        kst = mov1 + 2*mov2 + 3*mov3 + 4*mov4
        signal = talib.SMA(kst, timeperiod=9)

        self.df['KST'] = kst
        self.df['KST_signal'] = signal

    def _calculate_coppock(self):
        """Calculate Coppock Curve"""
        roc14 = talib.ROC(self.close, timeperiod=14)
        roc11 = talib.ROC(self.close, timeperiod=11)
        coppock = talib.WMA(roc14 + roc11, timeperiod=10)
        self.df['COPPOCK'] = coppock

    def _calculate_ultimate_oscillator(self, short=7, medium=14, long=28):
        """Calculate Ultimate Oscillator using proper rolling sums"""
        # True Range
        tr = talib.TRANGE(self.high, self.low, self.close)

        # Buying Pressure
        bp = self.close - np.minimum(self.low, np.roll(self.close, 1))
        bp[0] = self.close[0] - self.low[0]  # Handle first value

        # Calculate rolling sums (proper Ultimate Oscillator formula)
        bp_sum_7 = pd.Series(bp).rolling(window=short, min_periods=short).sum()
        tr_sum_7 = pd.Series(tr).rolling(window=short, min_periods=short).sum()

        bp_sum_14 = pd.Series(bp).rolling(window=medium, min_periods=medium).sum()
        tr_sum_14 = pd.Series(tr).rolling(window=medium, min_periods=medium).sum()

        bp_sum_28 = pd.Series(bp).rolling(window=long, min_periods=long).sum()
        tr_sum_28 = pd.Series(tr).rolling(window=long, min_periods=long).sum()

        # Calculate averages (avoid division by zero)
        avg7 = np.where(tr_sum_7 > 0, bp_sum_7 / tr_sum_7, 0)
        avg14 = np.where(tr_sum_14 > 0, bp_sum_14 / tr_sum_14, 0)
        avg28 = np.where(tr_sum_28 > 0, bp_sum_28 / tr_sum_28, 0)

        # Ultimate Oscillator
        ult_osc = 100 * (4 * avg7 + 2 * avg14 + avg28) / 7
        self.df['ULT_OSC'] = ult_osc

    def _calculate_supertrend(self, atr_period=10, multiplier=3):
        """Calculate Supertrend Indicator"""
        # Calculate ATR
        atr = talib.ATR(self.high, self.low, self.close, timeperiod=atr_period)

        # Calculate basic bands
        hl2 = (self.high + self.low) / 2
        upperband = hl2 + (multiplier * atr)
        lowerband = hl2 - (multiplier * atr)

        # Initialize arrays
        supertrend = np.zeros(len(self.close))
        in_uptrend = np.zeros(len(self.close), dtype=bool)

        # Set initial values
        in_uptrend[0] = True
        supertrend[0] = lowerband[0]

        # Calculate Supertrend
        for i in range(1, len(self.close)):
            if self.close[i] > upperband[i-1]:
                in_uptrend[i] = True
            elif self.close[i] < lowerband[i-1]:
                in_uptrend[i] = False
            else:
                in_uptrend[i] = in_uptrend[i-1]

                # Adjust bands
                if in_uptrend[i]:
                    lowerband[i] = max(lowerband[i], lowerband[i-1])
                else:
                    upperband[i] = min(upperband[i], upperband[i-1])

            # Set Supertrend value
            if in_uptrend[i]:
                supertrend[i] = lowerband[i]
            else:
                supertrend[i] = upperband[i]

        self.df['SUPERTREND'] = supertrend
        self.df['SUPERTREND_UPTREND'] = in_uptrend

    def _calculate_tsi(self, short=25, long=13, signal=7):
        """Calculate True Strength Index"""
        # Convert to pandas Series for calculations
        close_series = pd.Series(self.close)

        # Calculate price momentum
        momentum = close_series.diff()
        abs_momentum = momentum.abs()

        # Double smoothed momentum
        momentum_smooth1 = momentum.ewm(span=long).mean()
        momentum_smooth2 = momentum_smooth1.ewm(span=short).mean()

        # Double smoothed absolute momentum
        abs_momentum_smooth1 = abs_momentum.ewm(span=long).mean()
        abs_momentum_smooth2 = abs_momentum_smooth1.ewm(span=short).mean()

        # TSI calculation
        tsi = 100 * (momentum_smooth2 / abs_momentum_smooth2)
        tsi_signal = tsi.ewm(span=signal).mean()

        self.df['TSI'] = tsi.values
        self.df['TSI_signal'] = tsi_signal.values

    def _calculate_connors_rsi(self, rsi_period=3, streak_period=2, rank_period=100):
        """Calculate Connors RSI (simplified version)"""
        # Component 1: RSI(3)
        rsi_3 = talib.RSI(self.close, timeperiod=rsi_period)

        # Component 2: Streak RSI (simplified)
        # Calculate consecutive up/down streaks
        close_series = pd.Series(self.close)
        price_changes = close_series.diff()
        streaks = np.zeros(len(price_changes))
        current_streak = 0

        for i in range(1, len(price_changes)):
            if price_changes.iloc[i] > 0:
                current_streak = max(1, current_streak + 1) if current_streak > 0 else 1
            elif price_changes.iloc[i] < 0:
                current_streak = min(-1, current_streak - 1) if current_streak < 0 else -1
            else:
                current_streak = 0
            streaks[i] = current_streak

        streak_rsi = talib.RSI(pd.Series(streaks), timeperiod=streak_period)

        # Component 3: Percent Rank (simplified)
        percent_rank = close_series.rolling(window=rank_period).rank(pct=True) * 100

        # Connors RSI
        connors_rsi = (rsi_3 + streak_rsi + percent_rank) / 3
        self.df['CONNORS_RSI'] = connors_rsi

    def _calculate_vortex_indicator(self, period=14):
        """Calculate Vortex Indicator"""
        # Calculate VM+ and VM-
        vm_plus = np.abs(self.high - np.roll(self.low, 1))
        vm_minus = np.abs(self.low - np.roll(self.high, 1))

        # Calculate True Range
        tr = talib.TRANGE(self.high, self.low, self.close)

        # Rolling sums
        vm_plus_sum = pd.Series(vm_plus).rolling(window=period).sum()
        vm_minus_sum = pd.Series(vm_minus).rolling(window=period).sum()
        tr_sum = pd.Series(tr).rolling(window=period).sum()

        # VI+ and VI-
        vi_plus = vm_plus_sum / tr_sum
        vi_minus = vm_minus_sum / tr_sum

        self.df['VI_PLUS'] = vi_plus
        self.df['VI_MINUS'] = vi_minus

    def _calculate_donchian_channels(self, period=20):
        """Calculate Donchian Channels"""
        donchian_upper = pd.Series(self.high).rolling(window=period).max()
        donchian_lower = pd.Series(self.low).rolling(window=period).min()
        donchian_middle = (donchian_upper + donchian_lower) / 2

        self.df['DONCHIAN_UPPER'] = donchian_upper
        self.df['DONCHIAN_LOWER'] = donchian_lower
        self.df['DONCHIAN_MIDDLE'] = donchian_middle

    # ULTRA-FAST CACHED ACCESS METHODS
    def get_cached_value(self, indicator_name, idx):
        """Get cached indicator value for ultra-fast access"""
        if self.cache_engine is None:
            # Fallback to DataFrame access
            return self.df[indicator_name].iloc[idx] if indicator_name in self.df.columns else None

        # Calculate relative index for cached data
        cache_idx = idx - self.cache_start_idx
        if cache_idx < 0:
            return None

        # Map common indicator names to cached arrays
        cache_mapping = {
            'close': '_cached_close',
            'high': '_cached_high',
            'low': '_cached_low',
            'open': '_cached_open',
            'volume': '_cached_volume',
            'MA7': '_cached_ma7',
            'SMA_7': '_cached_ma7',
            'MA25': '_cached_ma25',
            'SMA_25': '_cached_ma25',
            'MA50': '_cached_ma50',
            'SMA_50': '_cached_ma50',
            'MA200': '_cached_ma200',
            'SMA_200': '_cached_ma200',
            'RSI': '_cached_rsi14',
            'BB_upper': '_cached_bb_upper',
            'BB_lower': '_cached_bb_lower',
            'BB_middle': '_cached_bb_middle',
            'MACD': '_cached_macd',
            'MACD_signal': '_cached_macd_signal',
            'volume_sma_20': '_cached_volume_ma',
            'volume_sma': '_cached_volume_ma',
            'STOCH_K': '_cached_stoch_k',
            'price_change': '_cached_price_change',
            'price_change_pct': '_cached_price_change'
        }

        cached_attr = cache_mapping.get(indicator_name)
        if cached_attr and hasattr(self.cache_engine, cached_attr):
            cached_array = getattr(self.cache_engine, cached_attr)
            if cache_idx < len(cached_array):
                return cached_array[cache_idx]

        # Fallback to DataFrame access
        return self.df[indicator_name].iloc[idx] if indicator_name in self.df.columns else None

    def get_ma(self, period, idx):
        """Get moving average with caching"""
        if period == 7:
            return self.get_cached_value('MA7', idx)
        elif period == 25:
            return self.get_cached_value('MA25', idx)
        elif period == 50:
            return self.get_cached_value('MA50', idx)
        elif period == 200:
            return self.get_cached_value('MA200', idx)
        else:
            return self.get_cached_value(f'SMA_{period}', idx)

    def get_rsi(self, idx, period=14):
        """Get RSI with caching"""
        if period == 14:
            return self.get_cached_value('RSI', idx)
        else:
            return self.get_cached_value(f'RSI_{period}', idx)

    def get_bb_value(self, band, idx):
        """Get Bollinger Band value with caching"""
        return self.get_cached_value(f'BB_{band}', idx)

    def get_price(self, price_type, idx):
        """Get price with caching"""
        return self.get_cached_value(price_type, idx)

    def get_volume(self, idx):
        """Get volume with caching"""
        return self.get_cached_value('volume', idx)

    def _calculate_kama(self, period=10, fast=2, slow=30):
        """Calculate Kaufman's Adaptive Moving Average"""
        close_series = pd.Series(self.close)

        # Calculate efficiency ratio
        change = close_series.diff(period).abs()
        volatility = close_series.diff().abs().rolling(window=period).sum()
        efficiency_ratio = change / volatility
        efficiency_ratio = efficiency_ratio.fillna(0)

        # Calculate smoothing constant
        smoothing_constant = ((efficiency_ratio * (fast - slow) + slow) / 100) ** 2

        # Initialize KAMA
        kama = pd.Series(index=close_series.index, dtype=float)
        kama.iloc[0] = close_series.iloc[0]

        for i in range(1, len(close_series)):
            kama.iloc[i] = kama.iloc[i-1] + smoothing_constant.iloc[i] * (close_series.iloc[i] - kama.iloc[i-1])

        self.df['KAMA'] = kama.values

    def _calculate_tema(self, period=14):
        """Calculate Triple Exponential Moving Average"""
        ema1 = talib.EMA(self.close, timeperiod=period)
        ema2 = talib.EMA(ema1, timeperiod=period)
        ema3 = talib.EMA(ema2, timeperiod=period)
        tema = 3 * (ema1 - ema2) + ema3
        self.df['TEMA'] = tema

    def _calculate_parabolic_sar_enhanced(self, af=0.02, max_af=0.2):
        """Calculate enhanced Parabolic SAR with more control"""
        psar = np.zeros(len(self.close))
        ep = np.zeros(len(self.close))
        af_series = np.zeros(len(self.close))

        # Initialize
        psar[0] = self.low[0]
        ep[0] = self.high[0]
        af_series[0] = af

        for i in range(1, len(self.close)):
            psar[i] = psar[i-1] + af_series[i-1] * (ep[i-1] - psar[i-1])

            if self.close[i] > psar[i]:
                psar[i] = min(psar[i], self.low[i])
                ep[i] = self.high[i]
                af_series[i] = min(af_series[i-1] + af, max_af)
            else:
                psar[i] = max(psar[i], self.high[i])
                ep[i] = self.low[i]
                af_series[i] = min(af_series[i-1] + af, max_af)

        self.df['PARABOLIC_SAR'] = psar

"""
Debug <PERSON>ript for Daily Drop Filter
Check if the daily drop filter is working correctly with real data
"""

import pandas as pd
import numpy as np
from config import FullAnalysisConfig
from backtesting_engine import BacktestingEngine, calculate_daily_drop_worker, check_global_market_filters_worker
from indicators import TechnicalIndicators


def debug_daily_drop_filter():
    """Debug the daily drop filter with real data"""
    print("🔍 DEBUGGING DAILY DROP FILTER")
    print("=" * 60)
    
    # Load real data
    try:
        df = pd.read_csv('selected_data.csv')
        print(f"✅ Loaded {len(df):,} candles from selected_data.csv")
    except:
        print("❌ Could not load selected_data.csv")
        return False
    
    # Calculate indicators
    print("📈 Calculating technical indicators...")
    indicators = TechnicalIndicators(df)
    df = indicators.calculate_all_indicators()
    
    # Test configuration
    config = FullAnalysisConfig()
    
    print(f"\n🔧 Configuration Check:")
    print(f"   - ENABLE_GLOBAL_MARKET_FILTERS: {config.ENABLE_GLOBAL_MARKET_FILTERS}")
    print(f"   - ENABLE_DAILY_DROP_FILTER: {config.ENABLE_DAILY_DROP_FILTER}")
    print(f"   - DAILY_DROP_THRESHOLD: {config.DAILY_DROP_THRESHOLD}%")
    print(f"   - GLOBAL_EMA200_FILTER: {config.GLOBAL_EMA200_FILTER}")
    print(f"   - GLOBAL_RSI_OVERBOUGHT_FILTER: {config.GLOBAL_RSI_OVERBOUGHT_FILTER}")
    
    # Initialize engine
    engine = BacktestingEngine(df, config)
    
    # Find periods with significant drops
    print(f"\n📊 Analyzing daily drops in dataset...")
    
    drop_periods = []
    blocked_periods = []
    
    # Test a sample of the data (every 100th candle for speed)
    sample_indices = range(1440, min(10000, len(df)), 100)  # Start after 1 day, sample every 100 candles
    
    for i in sample_indices:
        # Calculate daily drop
        daily_drop = calculate_daily_drop_worker(df, i)
        
        if daily_drop >= 1.0:  # Track drops >= 1%
            drop_periods.append({
                'index': i,
                'daily_drop': daily_drop,
                'price': df['close'].iloc[i],
                'time': f"Index_{i}"
            })
            
            # Test if filter blocks trades
            worker_config = {
                'ENABLE_GLOBAL_MARKET_FILTERS': config.ENABLE_GLOBAL_MARKET_FILTERS,
                'ENABLE_DAILY_DROP_FILTER': config.ENABLE_DAILY_DROP_FILTER,
                'DAILY_DROP_THRESHOLD': config.DAILY_DROP_THRESHOLD,
                'GLOBAL_EMA200_FILTER': config.GLOBAL_EMA200_FILTER,
                'GLOBAL_RSI_OVERBOUGHT_FILTER': config.GLOBAL_RSI_OVERBOUGHT_FILTER,
                'GLOBAL_RSI_OVERBOUGHT_THRESHOLD': config.GLOBAL_RSI_OVERBOUGHT_THRESHOLD,
            }
            
            # Test worker filter
            worker_blocked = not check_global_market_filters_worker(df, i, worker_config)
            
            # Test unified filter
            unified_blocked = not engine._check_global_market_filters(i)
            
            if daily_drop >= config.DAILY_DROP_THRESHOLD:
                blocked_periods.append({
                    'index': i,
                    'daily_drop': daily_drop,
                    'worker_blocked': worker_blocked,
                    'unified_blocked': unified_blocked,
                    'price': df['close'].iloc[i]
                })
    
    # Sort by daily drop
    drop_periods.sort(key=lambda x: x['daily_drop'], reverse=True)
    
    print(f"\n📈 Drop Analysis Results:")
    print(f"   - Total periods analyzed: {len(sample_indices):,}")
    print(f"   - Periods with ≥1% drop: {len(drop_periods)}")
    print(f"   - Periods with ≥{config.DAILY_DROP_THRESHOLD}% drop: {len(blocked_periods)}")
    
    if drop_periods:
        print(f"\n🔥 Top 10 Largest Daily Drops:")
        for i, period in enumerate(drop_periods[:10]):
            print(f"   {i+1:2d}. {period['time']}: {period['daily_drop']:.2f}% drop (Price: ${period['price']:.2f})")
    
    if blocked_periods:
        print(f"\n🚫 Periods That Should Be Blocked (≥{config.DAILY_DROP_THRESHOLD}%):")
        for period in blocked_periods[:5]:  # Show first 5
            worker_status = "✅ Blocked" if period['worker_blocked'] else "❌ NOT Blocked"
            unified_status = "✅ Blocked" if period['unified_blocked'] else "❌ NOT Blocked"
            print(f"   Index_{period['index']}: {period['daily_drop']:.2f}% drop")
            print(f"      Worker Filter: {worker_status}")
            print(f"      Unified Filter: {unified_status}")
        
        # Check if filters are working
        worker_working = sum(1 for p in blocked_periods if p['worker_blocked'])
        unified_working = sum(1 for p in blocked_periods if p['unified_blocked'])
        
        print(f"\n📊 Filter Effectiveness:")
        print(f"   - Worker filter blocking: {worker_working}/{len(blocked_periods)} ({worker_working/len(blocked_periods)*100:.1f}%)")
        print(f"   - Unified filter blocking: {unified_working}/{len(blocked_periods)} ({unified_working/len(blocked_periods)*100:.1f}%)")
        
        if worker_working == len(blocked_periods) and unified_working == len(blocked_periods):
            print("✅ Daily drop filter is working correctly!")
            return True
        else:
            print("❌ Daily drop filter is NOT working correctly!")
            return False
    else:
        print(f"⚠️ No periods found with ≥{config.DAILY_DROP_THRESHOLD}% drops in sample")
        print("This might mean:")
        print("   1. The market was stable during the test period")
        print("   2. The threshold is too high")
        print("   3. The calculation method needs adjustment")
        
        # Test with lower threshold
        print(f"\n🔍 Testing with lower threshold (1.5%)...")
        test_blocked = []
        for period in drop_periods:
            if period['daily_drop'] >= 1.5:
                i = period['index']
                worker_config = {
                    'ENABLE_GLOBAL_MARKET_FILTERS': True,
                    'ENABLE_DAILY_DROP_FILTER': True,
                    'DAILY_DROP_THRESHOLD': 1.5,
                    'GLOBAL_EMA200_FILTER': False,
                    'GLOBAL_RSI_OVERBOUGHT_FILTER': False,
                }
                worker_blocked = not check_global_market_filters_worker(df, i, worker_config)
                test_blocked.append(worker_blocked)
        
        if test_blocked:
            working_count = sum(test_blocked)
            print(f"   - With 1.5% threshold: {working_count}/{len(test_blocked)} periods blocked")
            if working_count == len(test_blocked):
                print("✅ Filter logic is working, just no major drops in your data!")
                return True
        
        return False


def test_specific_drop_scenario():
    """Test the filter with a manually created drop scenario"""
    print(f"\n🧪 Testing with Manual Drop Scenario")
    print("=" * 50)
    
    # Create test data with known drop
    test_data = []
    base_price = 50000
    
    # Create 1440 candles (1 day) with a significant drop
    for i in range(1440):
        if i < 720:  # First half - stable around 50k
            price = base_price + np.random.normal(0, 100)
        else:  # Second half - drop to 47k (6% drop)
            target_price = 47000
            progress = (i - 720) / 720
            price = base_price - (base_price - target_price) * progress + np.random.normal(0, 50)
        
        test_data.append({
            'close': price,
            'open': price + np.random.normal(0, 50),
            'high': price + abs(np.random.normal(0, 100)),
            'low': price - abs(np.random.normal(0, 100)),
            'volume': 1000 + np.random.normal(0, 100)
        })
    
    df = pd.DataFrame(test_data)
    
    # Test at the end (should show ~6% drop)
    test_idx = 1439
    daily_drop = calculate_daily_drop_worker(df, test_idx)
    
    print(f"📊 Manual test scenario:")
    print(f"   - Daily high: ~{base_price:.0f}")
    print(f"   - Current price: ~{df['close'].iloc[test_idx]:.0f}")
    print(f"   - Calculated drop: {daily_drop:.2f}%")
    
    # Test filter
    worker_config = {
        'ENABLE_GLOBAL_MARKET_FILTERS': True,
        'ENABLE_DAILY_DROP_FILTER': True,
        'DAILY_DROP_THRESHOLD': 3.0,
        'GLOBAL_EMA200_FILTER': False,
        'GLOBAL_RSI_OVERBOUGHT_FILTER': False,
    }
    
    filter_result = check_global_market_filters_worker(df, test_idx, worker_config)
    
    if daily_drop >= 3.0 and not filter_result:
        print("✅ Filter correctly blocked trades during 6% drop!")
        return True
    elif daily_drop < 3.0:
        print(f"⚠️ Drop calculation seems incorrect (expected ~6%, got {daily_drop:.2f}%)")
        return False
    else:
        print(f"❌ Filter failed to block trades during {daily_drop:.2f}% drop!")
        return False


def main():
    """Run debug analysis"""
    print("🔍 DAILY DROP FILTER DEBUG ANALYSIS")
    print("=" * 70)
    
    # Test 1: Real data analysis
    real_data_result = debug_daily_drop_filter()
    
    # Test 2: Manual scenario
    manual_test_result = test_specific_drop_scenario()
    
    print(f"\n📊 DEBUG SUMMARY:")
    print(f"   - Real data test: {'✅ PASS' if real_data_result else '❌ FAIL'}")
    print(f"   - Manual test: {'✅ PASS' if manual_test_result else '❌ FAIL'}")
    
    if real_data_result and manual_test_result:
        print("\n🎉 Daily drop filter is working correctly!")
        print("If you're still seeing identical results, it might be because:")
        print("   1. Your test period had no significant drops ≥3%")
        print("   2. The filter is working but other factors dominate results")
        print("   3. Try testing with a lower threshold (e.g., 1.5%) to see the effect")
    else:
        print("\n❌ Daily drop filter has issues!")
        print("Check the debug output above for specific problems.")


if __name__ == "__main__":
    main()

"""
Last Rules - 30 Specific Buy Rules
This file contains exactly the 30 buy rules specified by the user, copied exactly from selected_buy_rules_optimized.py
"""

import numpy as np
import pandas as pd


class LastRules:
    """The 30 specific buy rules requested"""
    
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.category = 'LAST'
        self.length = len(df)
        
        # Pre-cache column references for faster access
        self._columns = {}
        self._cache_columns()
    
    def _cache_columns(self):
        """Cache column references to avoid repeated dictionary lookups"""
        common_cols = [
            'close', 'open', 'high', 'low', 'volume',
            'RSI', 'MACD', 'MACD_signal', 'volume_ratio',
            'BB_upper', 'BB_lower', 'BB_middle', 'BB_position',
            'SMA_20', 'SMA_50', 'SMA_200', 'EMA_12', 'EMA_21', 'EMA_26', 'EMA_50',
            'MA_7', 'MA_25', 'MA_50', 'STOCH_K', 'STOCH_D', 'WILLIAMS_R',
            'ATR', 'ADX', 'PLUS_DI', 'MINUS_DI', 'MFI', 'CCI', 'CMF',
            'HMA_20', 'ROC_5', 'BB_width'
        ]
        
        for col in common_cols:
            if col in self.df.columns:
                self._columns[col] = self.df[col].values  # Use .values for fastest access
    
    def _get_values(self, column: str, indices: list) -> np.ndarray:
        """Fast batch data retrieval with bounds checking"""
        if column not in self._columns:
            return np.full(len(indices), np.nan)
        
        col_data = self._columns[column]
        result = np.full(len(indices), np.nan)
        
        for i, idx in enumerate(indices):
            if 0 <= idx < self.length:
                result[i] = col_data[idx]
        
        return result
    
    def _get_single(self, column: str, idx: int) -> float:
        """Fast single value retrieval"""
        if column not in self._columns or idx < 0 or idx >= self.length:
            return np.nan
        return self._columns[column][idx]
    
    def _is_valid(self, *values) -> bool:
        """Fast NaN check using direct comparison (faster than np.isnan)"""
        for val in values:
            if val != val:  # NaN != NaN is True, fastest NaN check
                return False
        return True
    
    def _max_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast max calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.max(self._columns[column][start_idx:end_idx])
    
    def _min_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast min calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.min(self._columns[column][start_idx:end_idx])
    
    def _mean_range(self, column: str, start_idx: int, end_idx: int) -> float:
        """Fast mean calculation over range"""
        if column not in self._columns:
            return np.nan
        
        start_idx = max(0, start_idx)
        end_idx = min(self.length, end_idx)
        
        if start_idx >= end_idx:
            return np.nan
        
        return np.mean(self._columns[column][start_idx:end_idx])

    def get_all_rules(self):
        """Get only the 30 specific rules requested"""
        return [
            ('Rule 21: Gap Up', self.rule_21_gap_up),
            ('New Rule 4: Ultimate Oscillator Breakout', self.new_rule_4_ultimate_oscillator_breakout),
            ('Rule 1: MA Alignment with RSI Oversold', self.rule_1_ma_alignment_with_rsi_oversold),
            ('SMC Rule 2: Fair Value Gap Fill', self.smc_rule_2_fair_value_gap_fill),
            ('AI Rule 10: Composite Sentiment Reversal', self.ai_rule_10_composite_sentiment_reversal),
            ('Rule 7: Bollinger Band Bounce', self.rule_7_bollinger_band_bounce),
            ('AI Rule 3: Smart Money Flow Divergence', self.ai_rule_3_smart_money_flow_divergence),
            ('Ext Rule 6: Fibonacci Support Confluence', self.ext_rule_6_fibonacci_support_confluence),
            ('Prof Rule 7: Mean Reversion Volatility Filter', self.prof_rule_7_mean_reversion_volatility_filter),
            ('Rule 28: Volume Breakout', self.rule_28_volume_breakout),
            ('AI Rule 8: Momentum Divergence Reversal', self.ai_rule_8_momentum_divergence_reversal),
            ('Professional Rule 10: CCI Reversal Enhanced', self.professional_rule_10_cci_reversal_enhanced),
            ('Volume Rule 5: Smart Money Volume', self.volume_rule_5_smart_money_volume),
            ('Professional Rule 7: Chaikin Money Flow Reversal', self.professional_rule_7_chaikin_money_flow_reversal),
            ('Volatility Rule 2: ATR Expansion Signal', self.volatility_rule_2_atr_expansion_signal),
            ('Volume Rule 4: Volume Breakout Confirmation', self.volume_rule_4_volume_breakout_confirmation),
            ('Rule 10: Volume Spike', self.rule_10_volume_spike),
            ('Momentum Rule 2: Momentum Divergence Recovery', self.momentum_rule_2_momentum_divergence_recovery),
            ('Rule 27: Structure Break Up', self.rule_27_structure_break_up),
            ('Momentum Rule 5: Momentum Breakout', self.momentum_rule_5_momentum_breakout),
            ('Rule 6: Stochastic Oversold Cross', self.rule_6_stochastic_oversold_cross),
            ('Advanced Rule 7: DMI ADX Filter', self.advanced_rule_7_dmi_adx_filter),
            ('SMC Rule 5: Institutional Candle Pattern', self.smc_rule_5_institutional_candle_pattern),
            ('Volume Rule 3: Dark Pool Activity', self.volume_rule_3_dark_pool_activity),
            ('Ext Rule 5: ATR Volatility Expansion', self.ext_rule_5_atr_volatility_expansion),
            ('Acad Rule 3: Volatility Breakout', self.acad_rule_3_volatility_breakout),
            ('Acad Rule 2: Mean Reversion Factor', self.acad_rule_2_mean_reversion_factor),
            ('Ext Rule 3: Bollinger Squeeze Breakout', self.ext_rule_3_bollinger_squeeze_breakout),
            ('Rule 2: Golden Cross', self.rule_2_golden_cross),
            ('Price Action Rule 3: Engulfing Pattern', self.price_action_rule_3_engulfing),
        ]

    # Rule 21: Gap Up (Optimized)
    def rule_21_gap_up(self, idx: int) -> bool:
        """Gap Up - Optimized"""
        if idx < 1:
            return False

        current_low = self._get_single('low', idx)
        prev_high = self._get_single('high', idx-1)
        volume_ratio = self._get_single('volume_ratio', idx)
        close = self._get_single('close', idx)
        open_price = self._get_single('open', idx)

        if not self._is_valid(current_low, prev_high, volume_ratio, close, open_price):
            return False

        return (current_low > prev_high and     # Gap up
                volume_ratio > 1.2 and         # Volume confirmation
                close > open_price)             # Strong close
